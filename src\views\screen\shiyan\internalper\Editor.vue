<template>
  <div class="card" id="card" ref="cardId" type="module" @click="handleCardClick" :class="{ 'card-expanded': showBanka }">
    <div class="card-inner" id="card-inner">
      <div class="left-panel">
        <div class="miniContainer" ref="miniContainer" id="miniContainer"></div>

        <div class="button-container" id="button-container">
          <div class="button-group" v-for="number in buttonCount" :key="number">
            <div class="custom-div"
              @click="handleButtonClick(number)"
              @mouseenter.stop="mouseEnterOpt(number)"
              @mouseleave.stop="mouseLeaveOpt(number)"
              :style="{ height: customDivHeight + 'px' }"
              :class="{ highlight: isOccupied(number) }"
              >
            </div>
            <div v-if="number % 5 === 0" class="custom-text">
              {{ number }}
            </div>
            <!-- 显示 ServerPopup 的条件 -->
            <!-- <ServerPopup v-if="currentClickedNumber === number"
            :showPopup="showPopup"
            @update:show-popup="visCustomDiv"
            @update-RmEquipmentList="updateRmEquipmentList"
            :RmEquipmentList="RmEquipmentList"
            :buttonCount = "buttonCount"
            :currentMouseNumber="currentMouseNumber"
            :Motor2parameters = "Motor2parameters"
            :currentClickedNumber="currentClickedNumber"
            :ServerState = "ServerState"
            :selectedCabinetId="selectedCabinet ? selectedCabinet.userData.cabinetData.uuid : ''"
            /> -->
          </div>
        </div>
      </div>

      <!-- 板卡组件 - 在button-container和right-panel之间 -->
      <!-- <div class="banka-slide-container" :class="{ 'banka-visible': showBanka }">
        <Banka ref="bankaComponent" />
      </div> -->

      <div class="right-panel">
        <div class="card-content" id="card-content">
          <div v-if="selectedCabinet" class="cabinet-info-container">
            <div class="cabinet-header">
              <h2 class="cabinet-title">
                <i class="el-icon-s-grid"></i>
                机柜信息
              </h2>
              <div class="cabinet-status-badge">
                <span class="status-dot"></span>
                {{ getCabinetStatus() }}
              </div>
            </div>

            <!-- 使用率概览 -->
            <div class="usage-overview">
              <div class="usage-stats">
                <div class="stat-item">
                  <div class="stat-icon">
                    <i class="el-icon-pie-chart"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ getUsagePercentage() }}%</div>
                    <div class="stat-label">使用率</div>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-icon">
                    <i class="el-icon-s-data"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ getUsedU() }}/{{ selectedCabinet.userData.cabinetData.totalU }}U</div>
                    <div class="stat-label">已用空间</div>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-icon">
                    <i class="el-icon-s-platform"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ getEquipmentCount() }}</div>
                    <div class="stat-label">设备数量</div>
                  </div>
                </div>
              </div>

              <!-- 使用率进度条 -->
              <div class="usage-progress">
                <div class="progress-header">
                  <span class="progress-label">空间使用情况</span>
                  <span class="progress-text">{{ getUsedU() }}U / {{ selectedCabinet.userData.cabinetData.totalU }}U</span>
                </div>
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: getUsagePercentage() + '%' }"></div>
                </div>
              </div>
            </div>

            <div class="cabinet-details">
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-document"></i>
                    机柜编码
                  </div>
                  <div class="info-value">{{ selectedCabinet.userData.cabinetData.cabinetCode }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-s-home"></i>
                    机柜名称
                  </div>
                  <div class="info-value">{{ selectedCabinet.userData.cabinetData.cabinetName }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-menu"></i>
                    机柜类型
                  </div>
                  <div class="info-value">{{ selectedCabinet.userData.cabinetData.cabinetType }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-user"></i>
                    创建人
                  </div>
                  <div class="info-value">{{ selectedCabinet.userData.cabinetData.createBy }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-location"></i>
                    归属房间
                  </div>
                  <div class="info-value">{{ selectedCabinet.userData.cabinetData.roomName }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-time"></i>
                    创建时间
                  </div>
                  <div class="info-value">{{ formatDate(selectedCabinet.userData.cabinetData.createTime) }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-s-cooperation"></i>
                    生产厂家
                  </div>
                  <div class="info-value">{{ selectedCabinet.userData.cabinetData.manufacturer || '未知' }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-position"></i>
                    位置坐标
                  </div>
                  <div class="info-value">X:{{ selectedCabinet.userData.cabinetData.positionX }}, Y:{{ selectedCabinet.userData.cabinetData.positionY }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-refresh"></i>
                    朝向角度
                  </div>
                  <div class="info-value">{{ getOrientationText(selectedCabinet.userData.cabinetData.orientation) }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-cpu"></i>
                    总U数
                  </div>
                  <div class="info-value">{{ selectedCabinet.userData.cabinetData.totalU }}U</div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-circle-check"></i>
                    运行状态
                  </div>
                  <div class="info-value">
                    <span :class="getStatusClass(selectedCabinet.userData.cabinetData.status)">
                      {{ getStatusText(selectedCabinet.userData.cabinetData.status) }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="dimensions-section">
                <h3 class="section-title">
                  <i class="el-icon-s-operation"></i>
                  尺寸规格
                </h3>
                <div class="dimensions-grid">
                  <div class="dimension-item">
                    <span class="dimension-label">长度</span>
                    <span class="dimension-value">{{ selectedCabinet.userData.cabinetData.length }}mm</span>
                  </div>
                  <div class="dimension-item">
                    <span class="dimension-label">宽度</span>
                    <span class="dimension-value">{{ selectedCabinet.userData.cabinetData.width }}mm</span>
                  </div>
                  <div class="dimension-item">
                    <span class="dimension-label">高度</span>
                    <span class="dimension-value">{{ selectedCabinet.userData.cabinetData.height }}mm</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 交换机数据展示区域 -->
        <div v-if="switchData" class="switch-data">
          <div class="switch-header">
            <h3 class="switch-title">
              <i class="el-icon-connection"></i>
              交换机数据
            </h3>
            <div class="switch-status-badge">
              <span class="status-dot active"></span>
              已连接
            </div>
          </div>
          <div class="switch-content">
            <pre class="switch-json">{{ JSON.stringify(switchData, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗组件 -->
  </div>
</template>

<script>
import { create } from 'sortablejs';
import ServerPopup from './Popup.vue'
// import Banka from './Banka.vue'
import { listRmRoom, getRmRoom, delRmRoom, addRmRoom, updateRmRoom } from "@/api/room/RmRoom";
import { listRmCabinet, getRmCabinet, delRmCabinet, addRmCabinet, updateRmCabinet } from "@/api/cabinet/RmCabinet";
import {listRmEquipment,getRmEquipment} from "@/api/equipment/RmEquipment";
import {updateShow3DPattern2,getAllJhj} from './Rendering/miniRender';
import { getParameters } from '../motor2';
import { update } from 'three/examples/jsm/libs/tween.module.js';
import { EventBus } from '@/utils/eventBus';

export default {
  name: 'Editor',
  components: { ServerPopup },

  props: {
    selectedCabinet: {
      type: Object,
    },
  },
  data() {
    return {
      message: '来自 Editor 组件的消息',
      buttonCount: null,
      customDivHeight: 0, // 新增：用于动态设置custom-div的高度
      clickedNumber: null, // 记录点击的按钮编号
      showPopup: false,
      showBanka: false, // 控制板卡组件的显示/隐藏
      currentClickedNumber: null, // 新增：记录当前点击的 number
      serverOptions: [
        { label: '请选择配置', value: '' },
        { label: '4U-12 标准配置', value: 'standard' },
        { label: '4U-12 增强配置', value: 'enhanced' }
      ],
      queryParams: {
          pageNum: 1,
          pageSize: 20,
          deptId: null,
          status: null,
          remarks: null,
          cabinetName: null,
          cabinetCode: null,
          roomId: null,
          roomName: null,
          totalU: null,
          manufacturer: null,
          cabinetType: null,
          length: null,
          width: null,
          height: null,
          positionX: null,
          positionY: null,
          orientation: null,
          modelId: null,
          modelName: null
        },
          // 机柜模板管理表格数据
      RmCabinetModelList: [],
      RmRoomList: [],
      RmEquipmentList: [],
      total: 0,
      loading: false,
      show3DPattern: false, // 新增变量
      currentMouseNumber:null,
      Motor2parameters:null,
      occupiedU: [],
      startU:[],
      occupiedUForMotor2:[],
      startUForMotor2:[],
      ServerState: false,
      currentCabinetEquipments:null,
      AddEquipmentInfo:null,
      switchData: null, // 存储交换机数据

    };
  },
  async created() {
    await this.getList();
    // console.log('RmEquipmentList', this.RmEquipmentList);
    // console.log('this.startUForMotor2', this.startUForMotor2);
    // console.log('this.occupiedUForMotor2', this.occupiedUForMotor2);
    // getAllJhj已经在getList方法中调用，这里不需要重复调用
    this.$nextTick(() => {
      this.updateHighlightDisplay();
    });
    // 如果数组为空，输出警告
    if (!this.startU || !this.occupiedU || this.startU.length === 0 || this.occupiedU.length === 0) {
      console.warn('警告: startU 或 occupiedU 数组为空，高亮显示可能不会生效');
    }
  },

  mounted() {
    // 确保页面加载时右侧面板是隐藏的
    const card = document.getElementById('card');
    if (card) {
      card.classList.add('hidden');
    }

    // 监听交换机点击事件
    this.initEventListeners();

    // 添加全局点击事件监听器，用于关闭Popup框
    document.addEventListener('click', this.handleGlobalClick);
  },

  methods: {
    handleGlobalClick(){
      this.showPopup = false;
      this.currentClickedNumber = null;
    },
    // 初始化事件监听器
    initEventListeners() {
      // 监听交换机点击事件
      this.$root.$on('switch-clicked', () => {
        this.showBanka = true;
        // 通知父组件交换机被点击，需要移动main页面
        this.$emit('switch-clicked');
      });

      // 监听交换机清除事件
      this.$root.$on('switch-cleared', () => {
        this.showBanka = false;
        this.switchData = null; // 清除交换机数据
        // 通知父组件交换机被清除，需要恢复main页面
        this.$emit('switch-cleared');
      });

      // 监听EventBus上的交换机点击事件，接收交换机数据
      EventBus.$on('switch-clicked', (data) => {
        // 保存初始数据
        this.switchData = data;

        // 如果userData中有交换机数据且有uuid，则通过API获取详细数据
        if (data.userData && data.userData.switchData && data.userData.switchData.uuid) {
          this.fetchSwitchData(data.userData.switchData.uuid);
        }
      });

      // 监听EventBus上的交换机清除事件
      EventBus.$on('switch-cleared', () => {
        this.switchData = null;
      });
    },

    // 获取交换机详细数据
    fetchSwitchData(uuid) {
      getRmEquipment(uuid).then(response => {

        // 更新switchData，将API返回的数据合并到原有数据中
        if (this.switchData && this.switchData.userData) {
          // 将API返回的数据存入userData
          this.switchData.userData.switchData = {
            ...this.switchData.userData.switchData,
            ...response.data
          };
        }
      }).catch(error => {
        console.error('获取交换机数据失败:', error);
      });
    },

    handleCardClick(event) {
      event.stopPropagation();
    },
    handleButtonClick(number) {
      // 如果点击的是同一个按钮，则切换Popup的显示状态
      if (this.currentClickedNumber === number) {
        this.showPopup = !this.showPopup;
      } else {
        // 如果点击的是不同的按钮，先关闭当前Popup，然后显示新的
        this.showPopup = false;

        // 使用nextTick确保DOM更新后再显示新的Popup
        this.$nextTick(() => {
          this.currentClickedNumber = number;
          this.showPopup = true;
        });
      }

      // 根据当前点击位置是否已被占用来设置ServerState
      // 如果位置未被占用，设置为true表示可以上架服务器
      // 如果位置已被占用，设置为false表示可以下架服务器
      this.ServerState = !this.isOccupied(number);

      // 确保在下一个DOM更新周期调整popup位置
      if (this.showPopup) {
        this.$nextTick(() => {
          // 获取子组件实例
          const popupRefs = this.$children.filter(child => child.$options.name === 'ServerPopup');
          if (popupRefs.length > 0) {
            // 调用子组件的adjustPopupPosition方法
            popupRefs.forEach(popup => {
              if (popup.adjustPopupPosition) {
                popup.adjustPopupPosition();
              }
            });
          }
        });
      }
    },
    handleAction(value) {
      this.showPopup = false;
    },
    mouseEnterOpt(number) {
      this.show3DPattern = true;
      this.currentMouseNumber = number;
      console.log('鼠标进入按钮', number);

      // 检查当前悬停的按钮是否在高亮区域内
      if (this.isOccupied(number)) {
        // 找到当前悬停按钮所在的连续高亮区域
        this.highlightContinuousArea(number, true);
      }
    },
    mouseLeaveOpt(number) {
      this.show3DPattern = false;
      this.currentMouseNumber = number;
      console.log('鼠标离开按钮', number);

      // 移除所有连续高亮区域的悬停效果
      this.highlightContinuousArea(number, false);
    },

    // 高亮或取消高亮连续区域
    highlightContinuousArea(number, isHighlight) {
      // 确保有高度数据
      const height = getParameters().height;

      // 找到当前number所在的连续区域
      for (let i = 0; i < this.startUForMotor2.length; i++) {
        let start = this.startUForMotor2[i];
        let occupied = this.occupiedUForMotor2[i];
        // 判断当前number是否在占用区间内
        if (number >= start && number < start + occupied) {
          // 找到了所在区域，为整个区域添加或移除高亮效果
          const allDivs = document.querySelectorAll('.custom-div');
          // 遍历所有的custom-div元素
          allDivs.forEach((div, index) => {
            // 索引从0开始，而我们的number从1开始，所以需要+1
            const divNumber = index + 1;

            // 检查是否在同一连续区域内
            if (divNumber >= start && divNumber < start + occupied) {
              if (isHighlight) {
                div.classList.add('highlight-hover');
              } else {
                div.classList.remove('highlight-hover');
              }
            }
          });

          // 找到区域后更新3D模型显示
          try {
            if (isHighlight) {
              // 传递实际的buttonCount和height
              updateShow3DPattern2(true, number, this.buttonCount, height, start, occupied);
            } else {
              // 传递实际的buttonCount和height
              updateShow3DPattern2(false, number, this.buttonCount, height, start, occupied);
            }
          } catch (error) {
            console.error('更新3D模型时出错:', error);
          }

          break; // 找到区域后退出循环
        }
      }
    },
    async getList() {
      this.loading = true;
      try {
          // 获取机房列表
          const RmRoomList = await listRmRoom(this.queryParams);
          this.RmRoomList = RmRoomList.rows;
          this.total = RmRoomList.total;

          // 获取机柜列表
          const RmCabinetModelList = await listRmCabinet(this.queryParams);
          this.RmCabinetModelList = RmCabinetModelList.rows;
          this.total = RmCabinetModelList.total;

          // 获取所有设备列表
          const RmEquipmentResponse = await listRmEquipment(this.queryParams);
          this.RmEquipmentList = RmEquipmentResponse.rows;
          this.total = RmEquipmentResponse.total;

          // 保存所有设备的startU和occupiedU（用于其他功能）
          this.startU = this.RmEquipmentList.map(item => item.startU);
          this.occupiedU = this.RmEquipmentList.map(item => item.occupiedU);

          // 清空当前机柜的设备数据
          this.currentCabinetEquipments = [];
          this.startUForMotor2 = [];
          this.occupiedUForMotor2 = [];

          // 如果有选中的机柜，筛选该机柜的设备
          if (this.selectedCabinet && this.selectedCabinet.userData && this.selectedCabinet.userData.cabinetData) {
              const cabinetUuid = this.selectedCabinet.userData.cabinetData.uuid;

              // 筛选出当前机柜的设备
              const currentCabinetEquipments = this.RmEquipmentList.filter(
                  (item) => item.cabinetId === cabinetUuid
              );

              // 保存当前机柜的设备列表
              this.currentCabinetEquipments = currentCabinetEquipments;
              console.log('currentCabinetEquipments', this.currentCabinetEquipments);

              // 更新startUForMotor2和occupiedUForMotor2数组
              this.startUForMotor2 = currentCabinetEquipments.map(
                  (item) => parseInt(item.startU)
              );
              this.occupiedUForMotor2 = currentCabinetEquipments.map(
                  (item) => parseInt(item.occupiedU)
              );

              // 将完整的设备数据传递给getAllJhj函数
              getAllJhj(
                this.startUForMotor2,
                this.occupiedUForMotor2,
                getParameters(),
                this.buttonCount,
                currentCabinetEquipments // 传递完整的设备数据
              );
          } else {
              // 如果没有选中机柜，清空3D模型
              getAllJhj([], [], getParameters(), this.buttonCount, []);
          }
      } catch (error) {
          console.error('Failed to fetch data:', error);
      } finally {
          this.loading = false;
      }
  },
    visCustomDiv(value) {
      this.showPopup = value;
      this.currentClickedNumber = null;

    },
    isOccupied(number) {
      // 如果未选中机柜或数据未加载，直接返回未占用
      if (!this.selectedCabinet ||
          !this.startUForMotor2.length ||
          !this.occupiedUForMotor2.length) {
        return false;
      }

      // 遍历当前机柜的设备占用数据
      for (let i = 0; i < this.startUForMotor2.length; i++) {
        const start = this.startUForMotor2[i];
        const occupied = this.occupiedUForMotor2[i];

        // 判断当前number是否在占用区间内
        if (number >= start && number < start + occupied) {
          return true;
        }
      }

      return false;
    },
    async updateRmEquipmentList(newEquipment) {
      // 保存当前点击的位置
      const currentPosition = this.currentClickedNumber;

      // 刷新设备列表数据
      await this.getList();

      // 如果当前已选中机柜，则更新该机柜的设备数据
      if (this.selectedCabinet && this.selectedCabinet.userData && this.selectedCabinet.userData.cabinetData) {
        const cabinetUuid = this.selectedCabinet.userData.cabinetData.uuid;

        // 重新筛选当前机柜的设备
        this.currentCabinetEquipments = this.RmEquipmentList.filter(
          (item) => item.cabinetId === cabinetUuid
        );

        // 更新startUForMotor2和occupiedUForMotor2数组
        this.startUForMotor2 = this.currentCabinetEquipments.map(
          (item) => parseInt(item.startU)
        );
        this.occupiedUForMotor2 = this.currentCabinetEquipments.map(
          (item) => parseInt(item.occupiedU)
        );

        // 检查是否是设备移除操作
        if (newEquipment.removed) {
          // 暂时关闭弹窗
          this.showPopup = false;

          // 设置ServerState为true，表示可以上架服务器
          this.ServerState = true;

          // 延迟一小段时间后重新显示弹窗，以便用户可以在同一位置上架新的服务器
          setTimeout(() => {
            // 恢复当前点击的位置
            this.currentClickedNumber = currentPosition;
            // 重新显示弹窗
            this.showPopup = true;

            // 确保在下一个DOM更新周期调整popup位置
            this.$nextTick(() => {
              // 获取子组件实例
              const popupRefs = this.$children.filter(child => child.$options.name === 'ServerPopup');
              if (popupRefs.length > 0) {
                // 调用子组件的adjustPopupPosition方法
                popupRefs.forEach(popup => {
                  if (popup.adjustPopupPosition) {
                    popup.adjustPopupPosition();
                  }
                });
              }
            });
          }, 500);
        }
      } else {
        // 如果没有选中机柜，清空数据
        this.currentCabinetEquipments = [];
        this.startUForMotor2 = [];
        this.occupiedUForMotor2 = [];
      }

      // 更新3D模型和高亮显示
      getAllJhj(this.startUForMotor2, this.occupiedUForMotor2, getParameters(), this.buttonCount, this.currentCabinetEquipments);
      this.updateHighlightDisplay();

      console.log('更新后的startUForMotor2:', this.startUForMotor2);
      console.log('更新后的occupiedUForMotor2:', this.occupiedUForMotor2);
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    // 获取机柜状态
    getCabinetStatus() {
      if (!this.selectedCabinet) return '离线';
      const status = this.selectedCabinet.userData.cabinetData.status;
      return status === '1' ? '在线' : '离线';
    },

    // 获取使用率百分比
    getUsagePercentage() {
      if (!this.selectedCabinet || !this.currentCabinetEquipments) return 0;
      const totalU = this.selectedCabinet.userData.cabinetData.totalU || 42;
      const usedU = this.getUsedU();
      return Math.round((usedU / totalU) * 100);
    },

    // 获取已使用的U数
    getUsedU() {
      if (!this.currentCabinetEquipments || this.currentCabinetEquipments.length === 0) return 0;
      return this.currentCabinetEquipments.reduce((total, equipment) => {
        return total + (parseInt(equipment.occupiedU) || 0);
      }, 0);
    },

    // 获取设备数量
    getEquipmentCount() {
      return this.currentCabinetEquipments ? this.currentCabinetEquipments.length : 0;
    },

    // 获取朝向文本
    getOrientationText(orientation) {
      const orientationMap = {
        0: '正北 (0°)',
        90: '正东 (90°)',
        180: '正南 (180°)',
        270: '正西 (270°)'
      };
      return orientationMap[orientation] || `${orientation}°`;
    },

    // 获取状态文本
    getStatusText(status) {
      return status === '1' ? '正常运行' : '停用';
    },

    // 获取状态样式类
    getStatusClass(status) {
      return status === '1' ? 'status-normal' : 'status-stopped';
    },

    // 更新高亮显示
    updateHighlightDisplay() {
      // 获取所有的custom-div元素
      const allDivs = document.querySelectorAll('.custom-div');

      // 先移除所有高亮和悬停高亮
      allDivs.forEach(div => {
        div.classList.remove('highlight');
        div.classList.remove('highlight-hover');
      });

      // 如果没有设备数据，直接返回
      if (!this.startUForMotor2 || !this.occupiedUForMotor2 || this.startUForMotor2.length === 0) {
        return;
      }

      // 为占用的U位添加高亮
      for (let i = 0; i < this.startUForMotor2.length; i++) {
        const start = this.startUForMotor2[i];
        const occupied = this.occupiedUForMotor2[i];

        // 遍历所有的custom-div元素
        allDivs.forEach((div, index) => {
          // 索引从0开始，而我们的number从1开始，所以需要+1
          const divNumber = index + 1;

          // 检查是否在占用区间内
          if (divNumber >= start && divNumber < start + occupied) {
            div.classList.add('highlight');
          }
        });
      }
    },

  },

  watch: {
    selectedCabinet(newCabinet, oldCabinet) {
      console.log('newCabinet====',newCabinet);

      // 当选择新机柜时，关闭Popup框
      this.showPopup = false;
      this.currentClickedNumber = null;

      // 清除旧数据和显示
      this.startUForMotor2 = [];
      this.occupiedUForMotor2 = [];
      this.currentCabinetEquipments = [];

      // 清除3D模型中的设备显示
      getAllJhj([], [], getParameters(), this.buttonCount, []);

      // 清除高亮显示
      this.updateHighlightDisplay();

      // 如果有新的机柜数据，则加载新机柜的设备
      if (newCabinet && newCabinet.userData && newCabinet.userData.cabinetData) {
        let totalU = newCabinet.userData.cabinetData.totalU;
        // 使用 totalU 来计算按钮数量
        this.buttonCount = Math.floor(totalU);
        this.customDivHeight = 800 / this.buttonCount;

        const cabinetUuid = newCabinet.userData.cabinetData.uuid;

        // 筛选出当前机柜的设备
        const currentCabinetEquipments = this.RmEquipmentList.filter(
          (item) => item.cabinetId === cabinetUuid
        );

        // 保存当前机柜的设备列表
        this.currentCabinetEquipments = currentCabinetEquipments;

        // 提取startU和occupiedU
        this.startUForMotor2 = currentCabinetEquipments.map(
          (item) => parseInt(item.startU)
        );
        this.occupiedUForMotor2 = currentCabinetEquipments.map(
          (item) => parseInt(item.occupiedU)
        );

        // 更新3D模型
        getAllJhj(this.startUForMotor2, this.occupiedUForMotor2, getParameters(), this.buttonCount, currentCabinetEquipments);

        // 更新高亮显示
        this.$nextTick(() => {
          this.updateHighlightDisplay();
        });
      }
      console.log('this.startUForMotor2', this.startUForMotor2);
      console.log('this.occupiedUForMotor2',this.occupiedUForMotor2);
    },
    currentCabinetEquipments(newValue){
      console.log('currentCabinetEquipments',newValue);

    },
    show3DPattern(newValue){
      this.$emit('updateShow3DPattern',newValue);
      this.customDivHeight = 800 / this.buttonCount;
      console.log('this.buttonCount',this.buttonCount);
      console.log('this.currentClickedNumber',this.currentClickedNumber);

      this.Motor2parameters = getParameters();
      const height = this.Motor2parameters.height;

      // 检查当前悬停的按钮是否在高亮区域内
      if (newValue && this.isOccupied(this.currentMouseNumber)) {
        // 找到当前悬停按钮所在的连续高亮区域
        // 这里不调用highlightContinuousArea，因为它会重复调用updateShow3DPattern2
        // 而是直接找到对应的区域并传递参数
        for (let i = 0; i < this.startUForMotor2.length; i++) {
          let start = this.startUForMotor2[i];
          let occupied = this.occupiedUForMotor2[i];
          if (this.currentMouseNumber >= start && this.currentMouseNumber < start + occupied) {
            // 找到了所在区域，直接调用updateShow3DPattern2
            // 传递实际的buttonCount和height
            updateShow3DPattern2(newValue, this.currentMouseNumber, this.buttonCount, height, start, occupied);
            return; // 找到区域后退出
          }
        }
      }

      // 如果不在高亮区域内，则使用普通的悬停效果
      // 传递实际的buttonCount和height
      updateShow3DPattern2(newValue, this.currentMouseNumber, this.buttonCount, height);
      // console.log('show3DPattern==',newValue);
    },
    startU: {
      handler() {
        // 数组变化时强制重新渲染
        this.$forceUpdate();
      },
      deep: true
    },
    occupiedU: {
      handler() {
        // 数组变化时强制重新渲染
        this.$forceUpdate();
      },
      deep: true
    }
  },
  beforeDestroy() {
    // 确保在组件销毁前关闭Popup框
    this.showPopup = false;
    this.currentClickedNumber = null;

    // 移除事件监听器
    this.$root.$off('switch-clicked');
    this.$root.$off('switch-cleared');
    EventBus.$off('switch-clicked');
    EventBus.$off('switch-cleared');
  }
};
</script>

<style scoped>
@keyframes pulse {
  0% {
    box-shadow: 0 0 8px rgba(0, 162, 255, 0.6);
    transform: scale(1.06);
  }
  50% {
    box-shadow: 0 0 12px rgba(0, 183, 255, 0.8);
    transform: scale(1.09);
  }
  100% {
    box-shadow: 0 0 8px rgba(0, 162, 255, 0.6);
    transform: scale(1.06);
  }
}

.custom-div.highlight {
  background: linear-gradient(135deg, rgba(0, 183, 255, 0.7), rgba(0, 120, 255, 0.5)) !important;
  border-color: #00a8ff !important;
  box-shadow: 0 0 8px rgba(0, 162, 255, 0.8) !important;
  position: relative;
  z-index: 2;
  transform: scale(1.06);
  transition: all 0.3s ease;
  animation: pulse 2s infinite ease-in-out;
}
/* 新增悬停连续区域高亮 */
.custom-div.highlight-hover {
  background: linear-gradient(135deg, rgba(255, 165, 0, 0.7), rgba(255, 140, 0, 0.5)) !important;
  border-color: #ffa500 !important;
  box-shadow: 0 0 8px rgba(255, 165, 0, 0.8) !important;
  animation: pulse 2s infinite ease-in-out;
}

/* 高亮单元格的悬停效果
.custom-div.highlight:hover {
  background: linear-gradient(135deg, rgba(0, 208, 255, 0.8), rgba(0, 140, 255, 0.7)) !important;
  box-shadow: 0 0 12px rgba(0, 183, 255, 0.9) !important;
  transform: translateY(-3px) scale(1.12) !important;
  border-color: #00d0ff !important;
  z-index: 4;
}
*/
.card {
  position: fixed !important;
  top: 85px !important;
  right: 0px !important;
  z-index: 10 !important; /* 提高z-index确保在其他元素之上 */
  height: 90% !important;
  width: 48% !important;
  display: flex !important; /* 改为flex以便显示内容 */
  flex-direction: row !important;
  color: rgb(255, 255, 255) !important;
  background: transparent !important; /* 设置为透明背景 */
  opacity: 1 !important;
  transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1) !important;
  /* border: 5px solid red !important; */
  transform: translateX(0) !important;
  will-change: transform, opacity, width !important; /* 提示浏览器这些属性将会变化，优化性能 */
  pointer-events: auto !important; /* 确保元素可以接收鼠标事件 */
  backdrop-filter: none !important; /* 确保没有背景滤镜效果 */
}

/* 当Banka显示时，扩展Editor宽度 */
.card.card-expanded {
  width: 50% !important; /* 增加宽度，确保有足够空间显示所有内容 */
}

#card.hidden {
  opacity: 0 !important;
  transform: translateX(100%) !important; /* 向右移出屏幕 */
  transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1) !important;
  pointer-events: none !important; /* 隐藏时不接收鼠标事件 */
  width: 0 !important; /* 隐藏时不占用空间 */
}

.card-inner{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  position: relative; /* 为绝对定位的子元素提供参考 */
  background: transparent !important; /* 设置为透明背景 */
  background-color: transparent !important; /* 确保背景色透明 */
  backdrop-filter: none !important; /* 确保没有背景滤镜效果 */
  border: none !important; /* 移除边框 */
}

.left-panel {
  width: 40%; /* 减小宽度，为banka留出空间 */
  height: 100%;
  display: flex;
  flex-direction: row;
  transition: width 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative; /* 确保子元素定位正确 */
  z-index: 1;
}

.right-panel {
  width: 59%; /* 增加宽度，确保内容显示完整 */
  height: 100%;
  display: flex;
  flex-direction: column; /* 改为列布局，使交换机数据显示在机柜信息下方 */
  padding-left: 10px;
  transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative; /* 确保子元素定位正确 */
  /* z-index: 1; */
  overflow-y: auto; /* 添加滚动条，确保内容过多时可以滚动 */
  background: transparent !important; /* 设置为透明背景 */
  background-color: transparent !important; /* 确保背景色透明 */
  backdrop-filter: none !important; /* 确保没有背景滤镜效果 */
  border: none !important; /* 移除边框 */
}

/* 当Banka显示时调整面板宽度 */
.card-expanded .left-panel {
  width: 40%; /* 保持不变 */
}

.card-expanded .right-panel {
  width: 50%; /* 调整宽度，为banka留出空间 */
  transform: translateX(50%); /* 使用transform代替margin-left，更平滑 */
  background: transparent !important; /* 设置为透明背景 */
  background-color: transparent !important; /* 确保背景色透明 */
  backdrop-filter: none !important; /* 确保没有背景滤镜效果 */
}

.miniContainer {
  /* border: blue 5px solid; */
  width: 100%;
  height: 100%;
  flex: 2;
  transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  border: none !important;
  outline: none !important;
}

/* 确保在banka显示时miniContainer保持正常大小 */
.card-expanded .miniContainer {
  width: 100%;
  height: 100%;
}

.card-content {
  /* border: red 5px solid; */
  flex: 1;
  margin-bottom: 20px;
  background: transparent !important; /* 设置为透明背景 */
  background-color: transparent !important; /* 确保背景色透明 */
  backdrop-filter: none !important; /* 确保没有背景滤镜效果 */
  border: none !important; /* 移除边框 */
}

/* 机柜信息容器 */
.cabinet-info-container {
  background: linear-gradient(135deg, rgba(0, 51, 89, 0.9), rgba(0, 40, 70, 0.8));
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.cabinet-info-container:hover {
  border-color: rgba(0, 162, 255, 0.6);
  box-shadow: 0 12px 40px rgba(0, 162, 255, 0.2);
  transform: translateY(-2px);
}

/* 使用率概览 */
.usage-overview {
  margin-bottom: 24px;
  padding: 20px;
  background: rgba(0, 40, 70, 0.4);
  border: 1px solid rgba(0, 162, 255, 0.2);
  border-radius: 10px;
}

.usage-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(0, 51, 89, 0.6);
  border: 1px solid rgba(0, 162, 255, 0.2);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(0, 51, 89, 0.8);
  border-color: rgba(0, 162, 255, 0.4);
  transform: translateY(-2px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #00a2ff, #0078d4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon i {
  font-size: 18px;
  color: #ffffff;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #00d0ff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* 使用率进度条 */
.usage-progress {
  margin-top: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
}

.progress-text {
  font-size: 12px;
  color: #00a2ff;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(0, 20, 40, 0.8);
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(0, 162, 255, 0.2);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00a2ff, #00d0ff);
  border-radius: 4px;
  transition: width 0.8s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 状态样式 */
.status-normal {
  color: #00ff7f;
  font-weight: 600;
}

.status-stopped {
  color: #ff6b6b;
  font-weight: 600;
}

/* 机柜头部 */
.cabinet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 162, 255, 0.2);
}

.cabinet-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #00a2ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cabinet-title i {
  font-size: 22px;
  color: #00d0ff;
}

.cabinet-status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(0, 255, 127, 0.1);
  border: 1px solid rgba(0, 255, 127, 0.3);
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 12px;
  color: #00ff7f;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #00ff7f;
  border-radius: 50%;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* 机柜详情 */
.cabinet-details {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  background: rgba(0, 51, 89, 0.4);
  border: 1px solid rgba(0, 162, 255, 0.2);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: rgba(0, 51, 89, 0.6);
  border-color: rgba(0, 162, 255, 0.4);
  transform: translateY(-1px);
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-label i {
  font-size: 14px;
  color: #00a2ff;
}

.info-value {
  font-size: 14px;
  color: #ffffff;
  font-weight: 600;
  word-break: break-all;
}

/* 尺寸规格部分 */
.dimensions-section {
  background: rgba(0, 40, 70, 0.5);
  border: 1px solid rgba(0, 162, 255, 0.2);
  border-radius: 10px;
  padding: 20px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #00d0ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  font-size: 18px;
}

.dimensions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.dimension-item {
  background: rgba(0, 51, 89, 0.6);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
  transition: all 0.3s ease;
}

.dimension-item:hover {
  background: rgba(0, 51, 89, 0.8);
  border-color: rgba(0, 162, 255, 0.5);
  transform: scale(1.02);
}

.dimension-label {
  display: block;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 4px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dimension-value {
  display: block;
  font-size: 16px;
  color: #00ff7f;
  font-weight: 700;
}

/* 交换机数据区域 */
.switch-data {
  background: linear-gradient(135deg, rgba(0, 51, 89, 0.9), rgba(0, 40, 70, 0.8));
  border: 1px solid rgba(255, 179, 0, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  max-height: 500px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.switch-data:hover {
  border-color: rgba(255, 179, 0, 0.6);
  box-shadow: 0 12px 40px rgba(255, 179, 0, 0.2);
  transform: translateY(-2px);
}

/* 交换机头部 */
.switch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 179, 0, 0.2);
}

.switch-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffb300;
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch-title i {
  font-size: 20px;
  color: #ffc107;
}

.switch-status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 179, 0, 0.1);
  border: 1px solid rgba(255, 179, 0, 0.3);
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 12px;
  color: #ffb300;
  font-weight: 500;
}

.switch-status-badge .status-dot.active {
  background: #ffb300;
}

/* 交换机内容 */
.switch-content {
  flex: 1;
  overflow: hidden;
  background: rgba(0, 40, 70, 0.5);
  border: 1px solid rgba(255, 179, 0, 0.2);
  border-radius: 8px;
  padding: 16px;
}

.switch-json {
  margin: 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(0, 20, 40, 0.6);
  border: 1px solid rgba(255, 179, 0, 0.1);
  border-radius: 6px;
  padding: 12px;
  max-height: 300px;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.switch-json:hover {
  background: rgba(0, 20, 40, 0.8);
  border-color: rgba(255, 179, 0, 0.2);
}

/* 自定义滚动条 */
.switch-json::-webkit-scrollbar {
  width: 6px;
}

.switch-json::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.switch-json::-webkit-scrollbar-thumb {
  background: rgba(255, 179, 0, 0.4);
  border-radius: 3px;
}

.switch-json::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 179, 0, 0.6);
}

.button-container {
  display: flex;
  flex-direction: column-reverse;
  align-items:start;
  border-left: 0.05px solid #276e76;
  /* gap: 1px;           按钮间距 */
  /* padding: 2px; */
  /* border: 1px solid red; */
  margin-right: 30px;
  height: 800px;
}

.button-group{
  display: flex;
}

.custom-div{
  background: #003159;
  border-top: 0.3px solid #000000;
  border-bottom: 0.3px solid #000000;
  border-right: none;
  cursor: pointer;
  font-size: 16px;
  color: #ffffff;
  transition: all 0.3s ease;
  width: 10px;

  &:hover {
    background: linear-gradient(to right, #00d0ff, #0088ff);
    color: rgb(255, 255, 255);
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 4px 10px rgba(0, 208, 255, 0.4);
    border-color: #00d0ff;
    z-index: 3;
  }

  /* 点击效果 */
  &:active {
    transform: translateY(0) scale(1.05);
    background: #0088ff;
    box-shadow: 0 2px 5px rgba(0, 136, 255, 0.3);
  }
}

.custom-button {
  width: 5px;
  height: 50px;
  background: #0d00ff;
  border: 2px solid #3498db;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  color: #ffffff;
  transition: all 0.3s ease;

  /* 悬停效果 */
  &:hover {
    background: #3498db;
    color: rgb(255, 255, 255);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  /* 点击效果 */
  &:active {
    transform: translateY(0);
    box-shadow: none;
  }
}
/* ServerPopup {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
} */

/* 板卡滑动容器 */
.banka-slide-container {
  position: absolute;
  left: 35%; /* 初始位置在button-container右侧 */
  top: 0;
  width: 0;
  height: 100%; /* 设置为页面高度 */
  background-color: transparent !important; /* 确保背景色透明 */
  border-radius: 5px;
  overflow: hidden;
  transition: all 0.5s ease;
  opacity: 0;
  z-index: 1; /* 降低z-index，确保不会覆盖其他元素 */
  box-shadow: none !important; /* 移除阴影 */
  border: none !important; /* 移除边框 */
  margin-left: 10px;
  backdrop-filter: none !important; /* 确保没有背景滤镜效果 */
}

/* 板卡可见状态 */
.banka-slide-container.banka-visible {
  width: 29%; /* 占据适当宽度，不覆盖right-panel */
  opacity: 1;
  background-color: transparent !important; /* 确保背景色透明 */
  backdrop-filter: none !important; /* 确保没有背景滤镜效果 */
}
</style>