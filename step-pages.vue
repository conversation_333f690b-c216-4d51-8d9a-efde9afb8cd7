<template>
  <div class="step-pages">
    <el-steps :active="activeStep" finish-status="success" align-center>
      <el-step title="步骤1"></el-step>
      <el-step title="步骤2"></el-step>
      <el-step title="步骤3"></el-step>
    </el-steps>

    <div class="step-content">
      <!-- 步骤1内容 -->
      <div v-show="activeStep === 0" class="step-item">
        <h3>步骤1页面</h3>
        <p>这是第一个步骤的页面内容</p>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>

      <!-- 步骤2内容 -->
      <div v-show="activeStep === 1" class="step-item">
        <h3>步骤2页面</h3>
        <p>这是第二个步骤的页面内容</p>
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>

      <!-- 步骤3内容 -->
      <div v-show="activeStep === 2" class="step-item">
        <h3>步骤3页面</h3>
        <p>这是第三个步骤的页面内容</p>
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="success" @click="finish">完成</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StepPages',
  data() {
    return {
      activeStep: 0
    };
  },
  methods: {
    nextStep() {
      if (this.activeStep < 2) {
        this.activeStep++;
      }
    },
    prevStep() {
      if (this.activeStep > 0) {
        this.activeStep--;
      }
    },
    finish() {
      this.$message.success('已完成所有步骤');
      // 可以在这里添加完成后的逻辑
    }
  }
};
</script>

<style scoped>
.step-pages {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.step-content {
  margin-top: 40px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  min-height: 200px;
}

.step-item h3 {
  margin-top: 0;
  color: #333;
}

.step-item p {
  color: #666;
  line-height: 1.6;
}

.step-item .el-button {
  margin-top: 20px;
  margin-right: 10px;
}
</style>