import TWEEN from '@tweenjs/tween.js';

// 调整main元素的位置
function adjustMainElement() {
    const mainElement = document.querySelector('.main');
    const leftElement = document.getElementById('left');

    if (mainElement) {
        // 让浏览器在下一帧重新计算布局
        requestAnimationFrame(() => {
            // 检查左侧面板是否隐藏
            const isLeftHidden = leftElement && leftElement.classList.contains('hidden');

            // 根据左侧面板的状态调整main元素的位置
            if (isLeftHidden) {
                // 左侧面板隐藏，main元素向左移动
                mainElement.style.transform = 'translateX(-8%)';
            } else {
                // 左侧面板显示，main元素恢复原位
                mainElement.style.transform = 'translateX(0)';
            }
        });
    }
}

export function addComposer(motor, outlinePass, updateSelectedCabinet, raycaster, card) {
    // 未选中对象返回空数组[],选中一个对象，数组1个元素，选中两个对象，数组两个元素
    const intersects = raycaster.intersectObjects([motor]); // 对参数中的网格模型对象进行射线交叉计算
    if (intersects.length === 0) {
        console.log("No intersection found");
        return { selectedObject: null, index: -1 };
    }
    let selectedObject = intersects[0].object; // 选中的第一个模型
    let index = outlinePass.selectedObjects.indexOf(selectedObject); // 获取选中对象在outlinePass中的索引

    if (index === -1) { // 如果对象不在发光列表中
        // 清除其他发光对象的动画
        outlinePass.selectedObjects.forEach(obj => {
            if (obj !== selectedObject) {
                new TWEEN.Tween(obj.position)
                    .to({ y: 11 }, 500)
                    .easing(TWEEN.Easing.Quadratic.Out)
                    .start();
            }
        });

        // 添加选中对象到发光列表
        outlinePass.selectedObjects = [selectedObject];

        // 机柜上升动画
        new TWEEN.Tween(selectedObject.position)
            .to({ y: 30 }, 500)
            .easing(TWEEN.Easing.Quadratic.Out)
            .start();

        // 显示右侧面板
        card.classList.remove('hidden');
        document.getElementById('left').classList.add('hidden');
        adjustMainElement();

    } else { // 如果对象已经在发光列表中
        // 从发光列表中移除
        outlinePass.selectedObjects.splice(index, 1);

        // 机柜下降动画
        new TWEEN.Tween(selectedObject.position)
            .to({ y: 11 }, 500)
            .easing(TWEEN.Easing.Quadratic.Out)
            .start();

        // 隐藏右侧面板
        card.classList.add('hidden');
        document.getElementById('left').classList.remove('hidden');
        adjustMainElement();
    }

    return { selectedObject, index };
}